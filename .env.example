# Database
DATABASE_URL="postgresql://username:password@localhost:5432/tourism_malaysia_ai"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Google Gemini AI
GEMINI_API_KEY="your-gemini-api-key"
GEMINI_API_URL="your-backend-api-url"

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# Cloudinary (for image storage)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Email (for notifications)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
EMAIL_FROM="<EMAIL>"

# App Configuration
NODE_ENV="development"
APP_URL="http://localhost:3000"
