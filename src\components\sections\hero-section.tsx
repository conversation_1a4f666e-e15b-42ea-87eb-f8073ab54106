'use client'

import { But<PERSON> } from '@/components/ui/button'
import { ArrowRight, MessageCircle, MapPin } from 'lucide-react'
import Link from 'next/link'
import { motion } from 'framer-motion'

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-malaysia-red/20 via-malaysia-blue/20 to-malaysia-yellow/20" />
        <div className="absolute inset-0 bg-black/40" />
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 text-center text-white">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto"
        >
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Discover Malaysia with{' '}
            <span className="malaysia-gradient bg-clip-text text-transparent">
              AI Intelligence
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl mb-8 text-gray-200 max-w-2xl mx-auto">
            Your personal AI travel companion for exploring the beauty, culture, and adventures that Malaysia has to offer.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link href="/chat">
              <Button size="lg" variant="malaysia" className="text-lg px-8 py-4">
                <MessageCircle className="mr-2 h-5 w-5" />
                Start Chatting
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            
            <Link href="/destinations">
              <Button size="lg" variant="outline" className="text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-black">
                <MapPin className="mr-2 h-5 w-5" />
                Explore Destinations
              </Button>
            </Link>
          </div>

          {/* Features Preview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto"
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <MessageCircle className="h-8 w-8 mb-4 mx-auto text-malaysia-yellow" />
              <h3 className="font-semibold mb-2">Natural Conversations</h3>
              <p className="text-sm text-gray-300">Chat naturally about your travel plans and get personalized recommendations</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <MapPin className="h-8 w-8 mb-4 mx-auto text-malaysia-yellow" />
              <h3 className="font-semibold mb-2">Local Insights</h3>
              <p className="text-sm text-gray-300">Discover hidden gems and authentic experiences across Malaysia</p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <ArrowRight className="h-8 w-8 mb-4 mx-auto text-malaysia-yellow" />
              <h3 className="font-semibold mb-2">Smart Planning</h3>
              <p className="text-sm text-gray-300">Get intelligent itinerary suggestions tailored to your preferences</p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
