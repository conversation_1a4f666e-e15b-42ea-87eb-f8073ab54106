{"name": "tourism-malaysia-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"next": "14.2.5", "react": "^18.3.1", "react-dom": "^18.3.1", "@prisma/client": "^5.15.0", "prisma": "^5.15.0", "next-auth": "^4.24.7", "@next-auth/prisma-adapter": "^1.0.7", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.23.8", "react-hook-form": "^7.52.1", "@hookform/resolvers": "^3.7.0", "axios": "^1.7.2", "@tanstack/react-query": "^5.51.1", "socket.io-client": "^4.7.5", "lucide-react": "^0.400.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.4.0", "framer-motion": "^11.3.2", "react-hot-toast": "^2.4.1", "date-fns": "^3.6.0"}, "devDependencies": {"typescript": "^5.5.3", "@types/node": "^20.14.10", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "tailwindcss": "^3.4.6", "autoprefixer": "^10.4.19", "postcss": "^8.4.39", "@types/eslint": "^8.56.10"}}