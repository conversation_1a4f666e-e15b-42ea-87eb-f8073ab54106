'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { ArrowRight, MapPin, Star } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

const featuredDestinations = [
  {
    id: 1,
    name: 'Kuala Lumpur',
    description: 'The vibrant capital city with iconic Petronas Twin Towers and diverse culture.',
    image: 'https://images.unsplash.com/photo-1596422846543-75c6fc197f07?w=500&h=300&fit=crop',
    rating: 4.5,
    category: 'City',
    highlights: ['Petronas Twin Towers', 'Street Food', 'Shopping']
  },
  {
    id: 2,
    name: 'Penang',
    description: 'UNESCO World Heritage site known for street art and incredible food.',
    image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=500&h=300&fit=crop',
    rating: 4.7,
    category: 'Cultural',
    highlights: ['George Town', 'Street Art', 'Local Cuisine']
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON>',
    description: 'Tropical paradise with pristine beaches and crystal-clear waters.',
    image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500&h=300&fit=crop',
    rating: 4.6,
    category: 'Nature',
    highlights: ['Beaches', 'Cable Car', 'Island Hopping']
  }
]

export function DestinationsPreview() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Featured{' '}
            <span className="malaysia-gradient bg-clip-text text-transparent">
              Destinations
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Discover some of Malaysia's most captivating destinations, each offering unique experiences and unforgettable memories.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {featuredDestinations.map((destination, index) => (
            <motion.div
              key={destination.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
              className="destination-card group cursor-pointer"
            >
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={destination.image}
                  alt={destination.name}
                  fill
                  className="object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-medium">
                  {destination.category}
                </div>
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-1 text-sm font-medium">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  {destination.rating}
                </div>
              </div>
              
              <div className="p-6">
                <h3 className="text-2xl font-bold mb-2 text-gray-900">
                  {destination.name}
                </h3>
                <p className="text-gray-600 mb-4 leading-relaxed">
                  {destination.description}
                </p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {destination.highlights.map((highlight) => (
                    <span
                      key={highlight}
                      className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm"
                    >
                      {highlight}
                    </span>
                  ))}
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-gray-500">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span className="text-sm">Malaysia</span>
                  </div>
                  <Button variant="ghost" size="sm" className="text-malaysia-blue hover:text-malaysia-red">
                    Learn More
                    <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <Link href="/destinations">
            <Button size="lg" variant="malaysia" className="text-lg px-8 py-4">
              Explore All Destinations
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </motion.div>
      </div>
    </section>
  )
}
