# Tourism Malaysia AI - Complete Setup Guide

This guide will walk you through setting up the complete Malaysia Tourism AI web application on your E: drive.

## 📋 Prerequisites Checklist

Before starting, ensure you have these installed:

- [ ] **Node.js 18+** - [Download here](https://nodejs.org/)
- [ ] **PostgreSQL** - [Download here](https://www.postgresql.org/download/)
- [ ] **Redis** - [Download here](https://redis.io/download) or use Redis Cloud
- [ ] **Git** - [Download here](https://git-scm.com/)

## 🗄 Database Setup

### Option 1: Local PostgreSQL Setup

1. **Install PostgreSQL** on your system
2. **Create a new database**:
   ```sql
   CREATE DATABASE tourism_malaysia_ai;
   CREATE USER tourism_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE tourism_malaysia_ai TO tourism_user;
   ```

### Option 2: Cloud Database (Recommended)

**Railway (Free tier available):**
1. Go to [Railway.app](https://railway.app/)
2. Sign up and create a new project
3. Add PostgreSQL service
4. Copy the connection string

**Supabase (Free tier available):**
1. Go to [Supabase.com](https://supabase.com/)
2. Create new project
3. Go to Settings > Database
4. Copy the connection string

## 🔧 Redis Setup

### Option 1: Local Redis
1. Install Redis on your system
2. Start Redis server: `redis-server`
3. Use connection string: `redis://localhost:6379`

### Option 2: Cloud Redis (Recommended)
**Upstash (Free tier available):**
1. Go to [Upstash.com](https://upstash.com/)
2. Create Redis database
3. Copy the connection string

## 🖼 Cloudinary Setup (Image Storage)

1. Go to [Cloudinary.com](https://cloudinary.com/)
2. Sign up for free account
3. Go to Dashboard
4. Copy your:
   - Cloud Name
   - API Key
   - API Secret

## 🔑 Google OAuth Setup (Optional)

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google`
   - `https://yourdomain.com/api/auth/callback/google`

## 🚀 Application Setup

### Step 1: Install Dependencies

```bash
cd e:\TourismMalaysiaAI
npm install
```

### Step 2: Environment Configuration

1. Copy the example file:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` with your actual values:
   ```env
   # Database - Replace with your actual database URL
   DATABASE_URL="postgresql://username:password@localhost:5432/tourism_malaysia_ai"

   # NextAuth.js - Generate a random secret
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-super-secret-key-min-32-chars"

   # Your existing Gemini AI setup
   GEMINI_API_KEY="your-existing-gemini-api-key"
   GEMINI_API_URL="your-existing-backend-api-url"

   # Redis
   REDIS_URL="redis://localhost:6379"

   # Cloudinary
   CLOUDINARY_CLOUD_NAME="your-cloud-name"
   CLOUDINARY_API_KEY="your-api-key"
   CLOUDINARY_API_SECRET="your-api-secret"

   # Google OAuth (optional)
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"
   ```

### Step 3: Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed with Malaysia tourism data
npx prisma db seed
```

### Step 4: Start Development Server

```bash
npm run dev
```

Visit `http://localhost:3000` to see your application!

## 🧪 Testing the Setup

### 1. Homepage Test
- Visit `http://localhost:3000`
- Should see the Malaysia Tourism AI homepage
- Check that all sections load properly

### 2. Database Test
- Run `npx prisma studio`
- Should open Prisma Studio at `http://localhost:5555`
- Verify that tables are created and seeded with data

### 3. Authentication Test
- Try to access `http://localhost:3000/auth/signin`
- Should see the sign-in page
- Test registration and login functionality

### 4. Chat Interface Test
- Visit `http://localhost:3000/chat`
- Should see the chat interface
- Test sending messages (requires your Gemini API to be working)

## 🔧 Troubleshooting

### Common Issues

**Database Connection Error:**
- Check your DATABASE_URL is correct
- Ensure PostgreSQL is running
- Verify database exists and user has permissions

**Redis Connection Error:**
- Check Redis is running locally or cloud URL is correct
- Verify REDIS_URL format

**Build Errors:**
- Run `npm run type-check` to check TypeScript errors
- Ensure all dependencies are installed: `npm install`

**Environment Variables:**
- Double-check all required variables are set
- Ensure no extra spaces in .env file
- Restart development server after changes

### Getting Help

1. Check the console for error messages
2. Review the logs in terminal
3. Verify all services are running
4. Check environment variable values

## 📱 Production Deployment

### Recommended Services

**Frontend Hosting:**
- **Vercel** (Recommended for Next.js)
- Netlify
- AWS Amplify

**Database:**
- Railway (Free tier)
- Supabase (Free tier)
- AWS RDS
- Google Cloud SQL

**Redis:**
- Upstash (Free tier)
- Redis Cloud
- AWS ElastiCache

### Deployment Steps

1. **Build the application:**
   ```bash
   npm run build
   ```

2. **Set environment variables** in your hosting platform

3. **Deploy database schema:**
   ```bash
   npx prisma migrate deploy
   ```

4. **Deploy the application** using your chosen platform

## 🎯 Next Steps

After successful setup:

1. **Customize the design** to match your brand
2. **Add more Malaysia destinations** to the database
3. **Integrate with your existing Gemini API**
4. **Test all functionality** thoroughly
5. **Deploy to production** when ready

## 📞 Support

If you encounter any issues during setup:

1. Check this guide again
2. Review error messages carefully
3. Ensure all prerequisites are met
4. Verify environment variables are correct

---

**Happy coding! 🇲🇾**
