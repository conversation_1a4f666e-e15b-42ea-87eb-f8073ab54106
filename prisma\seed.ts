import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database with Malaysia tourism data...')

  // Create sample destinations
  const kualaLumpur = await prisma.destination.create({
    data: {
      name: 'Kuala Lumpur',
      description: 'The vibrant capital city of Malaysia, known for its iconic Petronas Twin Towers, diverse culture, and amazing food scene.',
      location: {
        lat: 3.139,
        lng: 101.6869,
        address: 'Kuala Lumpur, Federal Territory of Kuala Lumpur, Malaysia'
      },
      images: [
        'https://images.unsplash.com/photo-1596422846543-75c6fc197f07',
        'https://images.unsplash.com/photo-1508062878650-88b52897f298'
      ],
      category: 'city',
      rating: 4.5,
      featured: true,
    },
  })

  const penang = await prisma.destination.create({
    data: {
      name: 'Penang',
      description: 'A UNESCO World Heritage site known for its rich history, street art, and incredible street food culture.',
      location: {
        lat: 5.4164,
        lng: 100.3327,
        address: 'Penang, Malaysia'
      },
      images: [
        'https://images.unsplash.com/photo-1559827260-dc66d52bef19',
        'https://images.unsplash.com/photo-1578662996442-48f60103fc96'
      ],
      category: 'cultural',
      rating: 4.7,
      featured: true,
    },
  })

  const langkawi = await prisma.destination.create({
    data: {
      name: 'Langkawi',
      description: 'A tropical paradise with pristine beaches, crystal-clear waters, and lush rainforests.',
      location: {
        lat: 6.3500,
        lng: 99.8000,
        address: 'Langkawi, Kedah, Malaysia'
      },
      images: [
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
        'https://images.unsplash.com/photo-1540979388789-6cee28a1cdc9'
      ],
      category: 'nature',
      rating: 4.6,
      featured: true,
    },
  })

  // Create attractions for Kuala Lumpur
  await prisma.attraction.create({
    data: {
      destinationId: kualaLumpur.id,
      name: 'Petronas Twin Towers',
      description: 'Iconic twin skyscrapers and symbol of Malaysia, featuring a sky bridge and observation deck.',
      coordinates: { lat: 3.1579, lng: 101.7116 },
      openingHours: {
        tuesday: '9:00-21:00',
        wednesday: '9:00-21:00',
        thursday: '9:00-21:00',
        friday: '9:00-21:00',
        saturday: '9:00-21:00',
        sunday: '9:00-21:00',
        monday: 'Closed'
      },
      entryFee: 'RM 85 (Adult), RM 35 (Child)',
      images: ['https://images.unsplash.com/photo-1596422846543-75c6fc197f07'],
      category: 'architectural',
      rating: 4.8,
    },
  })

  await prisma.attraction.create({
    data: {
      destinationId: kualaLumpur.id,
      name: 'Batu Caves',
      description: 'A limestone hill with a series of caves and cave temples, featuring a giant golden statue of Lord Murugan.',
      coordinates: { lat: 3.2379, lng: 101.6840 },
      openingHours: {
        monday: '6:00-21:00',
        tuesday: '6:00-21:00',
        wednesday: '6:00-21:00',
        thursday: '6:00-21:00',
        friday: '6:00-21:00',
        saturday: '6:00-21:00',
        sunday: '6:00-21:00'
      },
      entryFee: 'Free',
      images: ['https://images.unsplash.com/photo-1578662996442-48f60103fc96'],
      category: 'religious',
      rating: 4.5,
    },
  })

  // Create hotels for Kuala Lumpur
  await prisma.hotel.create({
    data: {
      destinationId: kualaLumpur.id,
      name: 'Mandarin Oriental Kuala Lumpur',
      description: 'Luxury hotel in the heart of Kuala Lumpur with stunning city views and world-class amenities.',
      priceRange: 'luxury',
      amenities: ['Pool', 'Spa', 'Gym', 'Restaurant', 'Bar', 'Concierge', 'WiFi'],
      coordinates: { lat: 3.1478, lng: 101.7089 },
      images: ['https://images.unsplash.com/photo-1566073771259-6a8506099945'],
      rating: 4.8,
      contactInfo: {
        phone: '+60 3-2380 8888',
        email: '<EMAIL>',
        website: 'https://www.mandarinoriental.com/kuala-lumpur'
      },
    },
  })

  // Create restaurants for Penang
  await prisma.restaurant.create({
    data: {
      destinationId: penang.id,
      name: 'Gurney Drive Hawker Centre',
      description: 'Famous hawker center offering authentic Penang street food including char kway teow, assam laksa, and cendol.',
      cuisineType: 'local',
      priceRange: 'budget',
      coordinates: { lat: 5.4370, lng: 100.3102 },
      images: ['https://images.unsplash.com/photo-1504674900247-0877df9cc836'],
      rating: 4.6,
      openingHours: {
        monday: '18:00-02:00',
        tuesday: '18:00-02:00',
        wednesday: '18:00-02:00',
        thursday: '18:00-02:00',
        friday: '18:00-02:00',
        saturday: '18:00-02:00',
        sunday: '18:00-02:00'
      },
      contactInfo: {
        phone: '+60 4-228 9599'
      },
    },
  })

  console.log('✅ Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
