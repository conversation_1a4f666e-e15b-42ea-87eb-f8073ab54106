// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  image         String?
  emailVerified DateTime?
  password      String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]
  sessions      Session[]
  conversations Conversation[]
  preferences   UserPreferences?
  itineraries   SavedItinerary[]
  favorites     UserFavorite[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Chat and Conversations
model Conversation {
  id        String   @id @default(cuid())
  userId    String
  title     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages Message[]

  @@map("conversations")
}

model Message {
  id             String   @id @default(cuid())
  conversationId String
  role           String // "user" or "assistant"
  content        String   @db.Text
  metadata       Json?    // For storing additional data like images, locations, etc.
  timestamp      DateTime @default(now())

  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@map("messages")
}

// Tourism Data
model Destination {
  id          String   @id @default(cuid())
  name        String
  description String   @db.Text
  location    Json     // {lat, lng, address}
  images      String[] // Array of image URLs
  category    String   // "city", "nature", "cultural", "adventure", etc.
  rating      Float?
  featured    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  attractions Attraction[]
  hotels      Hotel[]
  restaurants Restaurant[]
  favorites   UserFavorite[]

  @@map("destinations")
}

model Attraction {
  id            String   @id @default(cuid())
  destinationId String
  name          String
  description   String   @db.Text
  coordinates   Json     // {lat, lng}
  openingHours  Json?    // {monday: "9:00-17:00", ...}
  entryFee      String?
  images        String[]
  category      String   // "historical", "nature", "entertainment", etc.
  rating        Float?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  destination Destination    @relation(fields: [destinationId], references: [id], onDelete: Cascade)
  favorites   UserFavorite[]

  @@map("attractions")
}

model Hotel {
  id            String   @id @default(cuid())
  destinationId String
  name          String
  description   String   @db.Text
  priceRange    String   // "budget", "mid-range", "luxury"
  amenities     String[] // Array of amenities
  coordinates   Json     // {lat, lng}
  images        String[]
  rating        Float?
  contactInfo   Json?    // {phone, email, website}
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  destination Destination    @relation(fields: [destinationId], references: [id], onDelete: Cascade)
  favorites   UserFavorite[]

  @@map("hotels")
}

model Restaurant {
  id            String   @id @default(cuid())
  destinationId String
  name          String
  description   String   @db.Text
  cuisineType   String   // "malay", "chinese", "indian", "international", etc.
  priceRange    String   // "budget", "mid-range", "fine-dining"
  coordinates   Json     // {lat, lng}
  images        String[]
  rating        Float?
  openingHours  Json?    // {monday: "10:00-22:00", ...}
  contactInfo   Json?    // {phone, website}
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  destination Destination    @relation(fields: [destinationId], references: [id], onDelete: Cascade)
  favorites   UserFavorite[]

  @@map("restaurants")
}

// User Preferences and History
model UserPreferences {
  id                   String   @id @default(cuid())
  userId               String   @unique
  preferredDestinations String[] // Array of destination IDs or types
  budgetRange          String?  // "budget", "mid-range", "luxury"
  travelStyle          String?  // "adventure", "relaxation", "cultural", "family"
  interests            String[] // Array of interests
  language             String   @default("en")
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_preferences")
}

model SavedItinerary {
  id           String   @id @default(cuid())
  userId       String
  title        String
  description  String?  @db.Text
  destinations Json     // Array of destination objects with details
  duration     Int      // Duration in days
  totalBudget  Float?
  isPublic     Boolean  @default(false)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("saved_itineraries")
}

model UserFavorite {
  id        String   @id @default(cuid())
  userId    String
  itemType  String   // "destination", "attraction", "hotel", "restaurant"
  itemId    String   // ID of the favorited item
  createdAt DateTime @default(now())

  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  destination Destination? @relation(fields: [itemId], references: [id], onDelete: Cascade)
  attraction  Attraction?  @relation(fields: [itemId], references: [id], onDelete: Cascade)
  hotel       Hotel?       @relation(fields: [itemId], references: [id], onDelete: Cascade)
  restaurant  Restaurant?  @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@unique([userId, itemType, itemId])
  @@map("user_favorites")
}
