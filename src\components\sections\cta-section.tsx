'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Message<PERSON>ircle, ArrowR<PERSON>, Sparkles } from 'lucide-react'
import Link from 'next/link'

export function CTASection() {
  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 malaysia-gradient opacity-90" />
      <div className="absolute inset-0 bg-black/20" />
      
      {/* Decorative elements */}
      <div className="absolute top-10 left-10 w-20 h-20 border-2 border-white/20 rounded-full" />
      <div className="absolute bottom-10 right-10 w-32 h-32 border-2 border-white/20 rounded-full" />
      <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-white/20 rounded-full" />

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center text-white max-w-4xl mx-auto"
        >
          <div className="flex justify-center mb-6">
            <Sparkles className="h-16 w-16 text-malaysia-yellow" />
          </div>
          
          <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
            Ready to Explore Malaysia?
          </h2>
          
          <p className="text-xl md:text-2xl mb-8 text-white/90 max-w-2xl mx-auto">
            Start your journey today with our AI travel assistant. Get personalized recommendations, plan your itinerary, and discover the wonders of Malaysia.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Link href="/chat">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-4 bg-white text-malaysia-blue hover:bg-white/90">
                <MessageCircle className="mr-2 h-5 w-5" />
                Start Your Journey
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            
            <Link href="/auth/signup">
              <Button size="lg" variant="outline" className="text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-malaysia-blue">
                Create Free Account
              </Button>
            </Link>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto"
          >
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">1000+</div>
              <div className="text-white/80">Destinations</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">24/7</div>
              <div className="text-white/80">AI Assistant</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">100%</div>
              <div className="text-white/80">Free to Use</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
