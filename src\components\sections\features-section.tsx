'use client'

import { motion } from 'framer-motion'
import { 
  MessageSquare, 
  MapPin, 
  Calendar, 
  Heart, 
  Camera, 
  Users,
  Clock,
  Star
} from 'lucide-react'

const features = [
  {
    icon: MessageSquare,
    title: 'AI-Powered Conversations',
    description: 'Chat naturally with our AI assistant trained specifically on Malaysia tourism data and local insights.',
    color: 'text-malaysia-red'
  },
  {
    icon: MapPin,
    title: 'Destination Discovery',
    description: 'Explore hidden gems, popular attractions, and off-the-beaten-path locations across Malaysia.',
    color: 'text-malaysia-blue'
  },
  {
    icon: Calendar,
    title: 'Smart Itinerary Planning',
    description: 'Get personalized travel itineraries based on your interests, budget, and available time.',
    color: 'text-malaysia-yellow'
  },
  {
    icon: Heart,
    title: 'Personalized Recommendations',
    description: 'Receive tailored suggestions for food, activities, and experiences that match your preferences.',
    color: 'text-malaysia-red'
  },
  {
    icon: Camera,
    title: 'Cultural Insights',
    description: 'Learn about Malaysian culture, traditions, and etiquette to enhance your travel experience.',
    color: 'text-malaysia-blue'
  },
  {
    icon: Users,
    title: 'Local Experiences',
    description: 'Connect with authentic local experiences and community-recommended activities.',
    color: 'text-malaysia-yellow'
  },
  {
    icon: Clock,
    title: 'Real-time Information',
    description: 'Get up-to-date information about opening hours, weather, and local events.',
    color: 'text-malaysia-red'
  },
  {
    icon: Star,
    title: 'Quality Assurance',
    description: 'All recommendations are curated and verified to ensure the best travel experiences.',
    color: 'text-malaysia-blue'
  }
]

export function FeaturesSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Why Choose Our{' '}
            <span className="malaysia-gradient bg-clip-text text-transparent">
              AI Travel Assistant
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience Malaysia like never before with our intelligent travel companion that understands your needs and provides personalized guidance.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
              <div className="mb-4">
                <feature.icon className={`h-12 w-12 ${feature.color}`} />
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-900">
                {feature.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
