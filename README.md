# Tourism Malaysia AI - Web Application

A comprehensive AI-powered tourism web application specifically designed for Malaysia travel experiences. Built with Next.js 14, TypeScript, and integrated with Google Gemini 2.5 Flash AI model.

## 🌟 Features

- **AI-Powered Chat Interface**: Natural conversations with Malaysia tourism-focused AI
- **Destination Discovery**: Explore Malaysia's attractions, hotels, and restaurants
- **Smart Itinerary Planning**: Personalized travel recommendations
- **User Authentication**: Secure login/signup with NextAuth.js
- **Real-time Chat**: WebSocket-based chat functionality
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Database Integration**: PostgreSQL with Prisma ORM
- **File Storage**: Cloudinary integration for images
- **Caching**: Redis for performance optimization

## 🛠 Technology Stack

### Frontend
- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui
- **State Management**: TanStack Query (React Query)
- **Authentication**: NextAuth.js
- **Animations**: Framer Motion

### Backend & Database
- **Database**: PostgreSQL
- **ORM**: Prisma
- **Authentication**: NextAuth.js with JWT
- **Caching**: Redis
- **File Storage**: Cloudinary

### AI Integration
- **AI Model**: Google Gemini 2.5 Flash (pre-configured)
- **Backend API**: Your existing Gemini API endpoint

## 📋 Prerequisites

Before setting up the project, ensure you have:

- Node.js 18+ installed
- PostgreSQL database
- Redis server
- Cloudinary account (for image storage)
- Google OAuth credentials (optional)
- Your existing Gemini API endpoint

## 🚀 Installation & Setup

### 1. Clone and Install Dependencies

```bash
# Navigate to your project directory
cd e:\TourismMalaysiaAI

# Install dependencies
npm install
```

### 2. Environment Configuration

Copy the example environment file and configure your variables:

```bash
cp .env.example .env
```

Edit `.env` with your actual values:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/tourism_malaysia_ai"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Google Gemini AI
GEMINI_API_KEY="your-gemini-api-key"
GEMINI_API_URL="your-backend-api-url"

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# Cloudinary (for image storage)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# Google OAuth (optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### 3. Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Push database schema
npm run db:push

# Seed database with sample Malaysia tourism data
npx prisma db seed
```

### 4. Development Server

```bash
# Start development server
npm run dev
```

Visit `http://localhost:3000` to see your application.

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── chat/              # Chat interface
│   ├── destinations/      # Destinations pages
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   ├── sections/         # Page sections
│   └── providers.tsx     # Context providers
├── lib/                  # Utility libraries
│   ├── auth.ts          # NextAuth configuration
│   ├── prisma.ts        # Prisma client
│   └── utils.ts         # Helper functions
├── types/               # TypeScript type definitions
└── hooks/               # Custom React hooks

prisma/
├── schema.prisma        # Database schema
└── seed.ts             # Database seed data
```

## 🗄 Database Schema

The application includes comprehensive schemas for:

- **User Management**: Users, accounts, sessions
- **Chat System**: Conversations and messages
- **Tourism Data**: Destinations, attractions, hotels, restaurants
- **User Preferences**: Saved itineraries, favorites, preferences

## 🔧 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database
npm run db:migrate   # Run database migrations
npm run db:studio    # Open Prisma Studio
```

## 🚀 Deployment

### Recommended Deployment Stack

1. **Frontend**: Vercel (seamless Next.js integration)
2. **Database**: Railway, Supabase, or AWS RDS
3. **Redis**: Upstash or Redis Cloud
4. **File Storage**: Cloudinary

### Environment Variables for Production

Ensure all environment variables are properly set in your deployment platform:

- Database connection string
- NextAuth configuration
- API keys for external services
- Redis connection string

## 🔐 Security Features

- JWT-based authentication
- Password hashing with bcryptjs
- CSRF protection
- Input validation with Zod
- Secure session management
- Environment variable protection

## 🎨 Customization

### Malaysia-Themed Design

The application includes Malaysia-specific design elements:

- National colors (Red, Blue, Yellow)
- Cultural imagery and icons
- Local typography and styling
- Responsive design for mobile users

### Adding New Features

1. **New API Routes**: Add to `src/app/api/`
2. **New Pages**: Add to `src/app/`
3. **New Components**: Add to `src/components/`
4. **Database Changes**: Update `prisma/schema.prisma`

## 📱 Mobile Responsiveness

The application is fully responsive and optimized for:
- Mobile phones (iOS/Android)
- Tablets
- Desktop computers
- Various screen sizes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Review the code comments
- Open an issue on GitHub

---

**Built with ❤️ for Malaysia Tourism**
