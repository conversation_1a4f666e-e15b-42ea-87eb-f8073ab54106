import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from 'react-hot-toast'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Tourism Malaysia AI - Your Intelligent Travel Companion',
  description: 'Discover Malaysia with our AI-powered tourism assistant. Get personalized recommendations, plan your itinerary, and explore the best of Malaysia.',
  keywords: 'Malaysia tourism, travel AI, Malaysia travel guide, tourism assistant, Malaysia destinations',
  authors: [{ name: 'Tourism Malaysia AI Team' }],
  openGraph: {
    title: 'Tourism Malaysia AI - Your Intelligent Travel Companion',
    description: 'Discover Malaysia with our AI-powered tourism assistant',
    url: 'https://tourism-malaysia-ai.vercel.app',
    siteName: 'Tourism Malaysia AI',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Tourism Malaysia AI',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tourism Malaysia AI - Your Intelligent Travel Companion',
    description: 'Discover Malaysia with our AI-powered tourism assistant',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster 
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </Providers>
      </body>
    </html>
  )
}
